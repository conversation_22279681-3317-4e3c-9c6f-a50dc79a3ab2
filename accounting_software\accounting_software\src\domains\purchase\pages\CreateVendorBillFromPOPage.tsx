import React, { useState, useEffect } from 'react';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import {
  Box,
  Paper,
  Typography,
  Grid,
  TextField,
  Button,
  Autocomplete,
  Alert,
  Snackbar,
  Divider,
  Card,
  CardContent,
  Chip,
  MenuItem,
  CircularProgress,
  Backdrop,
  IconButton,
  Avatar,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Save as SaveIcon,
  SaveAlt as SaveAltIcon,
  Print as PrintIcon,
  Cancel as CancelIcon,
  Receipt as ReceiptIcon,
  Description as DescriptionIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Assignment as AssignmentIcon,
  Person as PersonIcon,
  ShoppingCart as ShoppingCartIcon,
} from '@mui/icons-material';
import dayjs from 'dayjs';

// Import services and types
import { vendorBillService, type VendorBill } from '../../../services/vendor-bill.service';
import { purchaseOrderService, type PurchaseOrder } from '../../../services/purchaseOrder.service';
import { vendorService, type Vendor } from '../../../services/vendor.service';
import { usePaymentTerms, type PaymentTerm } from '../../../contexts/PaymentTermsContext';
import { loadChartOfAccountsFast } from '../../../services/gl.service';
import api from '../../../services/api';

// Import shared components
import { StandardDatePicker } from '../../../shared/components';
import VendorBillLineTable, { VendorBillLineItem, ProductOption } from '../../../shared/components/VendorBillLineTable';
import { formatCurrency } from '../../../shared/utils/formatters';
import { useCurrencyInfo } from '../../gl/hooks/useCurrencyInfo';
import type { Account } from '../../../shared/types/gl.types';

interface SnackbarState {
  open: boolean;
  message: string;
  severity: 'success' | 'error' | 'warning' | 'info';
}

interface VendorBillFormData {
  vendor_id: number | null;
  vendor_name: string;
  liability_account_id: number | null;
  bill_date: string;
  due_date: string;
  reference_number: string;
  status: 'draft' | 'posted';
  payment_terms_id: number | null;
  payment_terms_name: string;
  notes: string;
  line_items: VendorBillLineItem[];
  subtotal: number;
  tax_amount: number;
  total_amount: number;
  source_type: 'po';
  source_document_id: number | null;
  po_number?: string;
}

const CreateVendorBillFromPOPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { currencyInfo, loading: currencyLoading } = useCurrencyInfo();

  // Get PO ID from URL params
  const poId = searchParams.get('po_id');

  // State
  const [loading, setLoading] = useState(false);
  const [poLoading, setPOLoading] = useState(true);
  const [vendorsLoading, setVendorsLoading] = useState(true);
  const [accountsLoading, setAccountsLoading] = useState(true);
  const [selectedPO, setSelectedPO] = useState<PurchaseOrder | null>(null);
  const [availablePOs, setAvailablePOs] = useState<PurchaseOrder[]>([]);
  const [showPOSelection, setShowPOSelection] = useState(!poId); // Show PO selection if no PO ID provided
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [products, setProducts] = useState<ProductOption[]>([]);
  const [liabilityAccounts, setLiabilityAccounts] = useState<Account[]>([]);
  const [snackbar, setSnackbar] = useState<SnackbarState>({
    open: false,
    message: '',
    severity: 'success',
  });

  // Payment terms context
  const { paymentTerms, loading: paymentTermsLoading, getDefaultPaymentTerm } = usePaymentTerms();

  // Calculate due date based on bill date and payment terms
  const calculateDueDate = (billDate: string, paymentTermId: number | null): string => {
    if (!paymentTermId) return billDate;

    const paymentTerm = paymentTerms.find(pt => pt.id === paymentTermId);
    if (!paymentTerm) return billDate;

    const date = dayjs(billDate);
    return date.add(paymentTerm.days, 'day').format('YYYY-MM-DD');
  };

  // Form data
  const [formData, setFormData] = useState<VendorBillFormData>({
    vendor_id: null,
    vendor_name: '',
    liability_account_id: null,
    bill_date: dayjs().format('YYYY-MM-DD'),
    due_date: dayjs().add(30, 'day').format('YYYY-MM-DD'),
    reference_number: '',
    status: 'draft',
    payment_terms_id: null,
    payment_terms_name: 'Net 30',
    notes: '',
    line_items: [],
    subtotal: 0,
    tax_amount: 0,
    total_amount: 0,
    source_type: 'po',
    po_id: null,
    po_number: '',
  });

  // Load Purchase Order data
  const loadPurchaseOrder = async (poId: string) => {
    try {
      setPOLoading(true);
      console.log('Loading PO:', poId);

      const po = await purchaseOrderService.getPurchaseOrder(parseInt(poId));
      console.log('Loaded PO data:', po);

      setSelectedPO(po);

      // Auto-populate form data from PO
      populateFormFromPO(po);

      console.log('Form data set from PO');

    } catch (error) {
      console.error('Failed to load PO:', error);
      setSnackbar({
        open: true,
        message: `Failed to load Purchase Order: ${error instanceof Error ? error.message : 'Unknown error'}`,
        severity: 'error',
      });
    } finally {
      setPOLoading(false);
    }
  };

  // Load initial data
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        setLoading(true);

        // Load accounts and products in parallel
        const [accountsData, productsData] = await Promise.all([
          loadChartOfAccountsFast().catch(err => {
            console.warn('Failed to load accounts:', err);
            return { accounts: [] };
          }),
          api.get('/sales/products/').catch(err => {
            console.warn('Failed to load products:', err);
            return { data: { results: [] } };
          })
        ]);

        // Extract accounts array from the response
        const accountsList = accountsData?.accounts || accountsData?.results || accountsData || [];
        setLiabilityAccounts(accountsList);
        setAccountsLoading(false);

        // Set products for the form
        setProducts(productsData.data?.results || []);

        console.log('Initial data loaded:', {
          accounts: accountsList.length,
          products: productsData.data?.results?.length || 0
        });

      } catch (error) {
        console.error('Failed to load initial data:', error);
        setSnackbar({
          open: true,
          message: 'Failed to load initial data',
          severity: 'error',
        });
      } finally {
        setLoading(false);
      }
    };

    loadInitialData();
  }, []);

  // Load available POs for selection
  const loadAvailablePOs = async () => {
    try {
      const pos = await purchaseOrderService.getPurchaseOrders();
      // Filter POs that can be used for bill creation (received, acknowledged, partial)
      const billablePOs = pos.filter(po =>
        ['received', 'acknowledged', 'partial'].includes(po.status)
      );
      setAvailablePOs(billablePOs);
    } catch (error) {
      console.error('Failed to load POs:', error);
      setSnackbar({
        open: true,
        message: 'Failed to load Purchase Orders',
        severity: 'error',
      });
    }
  };

  // Load PO when poId is available and accounts are loaded, or load available POs for selection
  useEffect(() => {
    if (poId && !accountsLoading && paymentTerms.length > 0) {
      loadPurchaseOrder(poId);
    } else if (!poId && !accountsLoading && paymentTerms.length > 0) {
      // No specific PO ID provided, load available POs for selection
      loadAvailablePOs();
    }
  }, [poId, accountsLoading, paymentTerms]);

  // Set default payment term when payment terms load
  useEffect(() => {
    if (paymentTerms.length > 0 && !formData.payment_terms_id && !selectedPO) {
      const defaultTerm = getDefaultPaymentTerm() || paymentTerms.find(pt => pt.code === 'net_30') || paymentTerms[0];
      if (defaultTerm) {
        setFormData(prev => ({
          ...prev,
          payment_terms_id: defaultTerm.id,
          payment_terms_name: defaultTerm.name,
          due_date: calculateDueDate(prev.bill_date, defaultTerm.id)
        }));
      }
    }
  }, [paymentTerms, formData.payment_terms_id, selectedPO, getDefaultPaymentTerm]);

  // Handle field changes
  const handleFieldChange = (field: keyof VendorBillFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle payment terms change
  const handlePaymentTermsChange = (paymentTermId: number | null) => {
    const paymentTerm = paymentTerms.find(pt => pt.id === paymentTermId);
    setFormData(prev => ({
      ...prev,
      payment_terms_id: paymentTermId,
      payment_terms_name: paymentTerm?.name || '',
      due_date: calculateDueDate(prev.bill_date, paymentTermId)
    }));
  };

  // Handle bill date change
  const handleBillDateChange = (newDate: string) => {
    setFormData(prev => ({
      ...prev,
      bill_date: newDate,
      due_date: calculateDueDate(newDate, prev.payment_terms_id)
    }));
  };

  // Handle line items change
  const handleLineItemsChange = (lineItems: VendorBillLineItem[]) => {
    const subtotal = lineItems.reduce((sum, item) => sum + item.line_total, 0);
    const taxAmount = lineItems.reduce((sum, item) => sum + (item.tax_amount || 0), 0);
    const totalAmount = subtotal + taxAmount;

    setFormData(prev => ({
      ...prev,
      line_items: lineItems,
      subtotal,
      tax_amount: taxAmount,
      total_amount: totalAmount,
    }));
  };

  // Create vendor bill from PO using backend endpoint
  const handleCreateFromPO = async () => {
    if (!selectedPO) {
      setSnackbar({
        open: true,
        message: 'No Purchase Order selected',
        severity: 'error',
      });
      return;
    }

    try {
      setLoading(true);
      console.log('Creating vendor bill from PO:', selectedPO.id);

      // Use the backend endpoint to create bill from PO
      const createdBill = await vendorBillService.createVendorBillFromPO(selectedPO.id!);

      console.log('Vendor bill created from PO:', createdBill);

      setSnackbar({
        open: true,
        message: `Vendor bill ${createdBill.bill_number} created successfully from PO ${selectedPO.po_number}!`,
        severity: 'success',
      });

      // Navigate to the created bill for editing
      setTimeout(() => {
        navigate(`/purchase/vendor-bills/${createdBill.id}/edit`);
      }, 1500);

    } catch (error) {
      console.error('Failed to create vendor bill from PO:', error);
      setSnackbar({
        open: true,
        message: `Failed to create vendor bill: ${error instanceof Error ? error.message : 'Unknown error'}`,
        severity: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  // Save customized vendor bill
  const handleSaveCustomized = async (closeAfterSave: boolean = false) => {
    if (!formData.vendor_id) {
      setSnackbar({
        open: true,
        message: 'Please select a vendor',
        severity: 'error',
      });
      return;
    }

    if (formData.line_items.length === 0) {
      setSnackbar({
        open: true,
        message: 'Please add at least one line item',
        severity: 'error',
      });
      return;
    }

    try {
      setLoading(true);
      console.log('Saving customized vendor bill...');

      const billData = {
        vendor: formData.vendor_id!,
        bill_date: formData.bill_date,
        due_date: formData.due_date,
        status: formData.status,
        payment_terms: formData.payment_terms_name,
        reference_number: formData.reference_number,
        notes: formData.notes,
        subtotal: formData.subtotal,
        tax_amount: formData.tax_amount,
        total_amount: formData.total_amount,
        amount_paid: 0,
        balance_due: formData.total_amount,
        line_items: formData.line_items.map((item, index) => ({
          product: item.product_id,
          item_description: item.item_description,
          quantity: item.quantity,
          unit_price: item.unit_price,
          tax_rate: item.tax_rate,
          account_code: item.account_code,
          line_order: index + 1,
        })),
      };

      console.log('Saving bill data:', billData);

      const savedBill = await vendorBillService.createVendorBill(billData);

      setSnackbar({
        open: true,
        message: `Vendor bill ${savedBill.bill_number} saved successfully!`,
        severity: 'success',
      });

      if (closeAfterSave) {
        setTimeout(() => {
          navigate('/purchase/vendor-bills');
        }, 1500);
      } else {
        setTimeout(() => {
          navigate(`/purchase/vendor-bills/${savedBill.id}/edit`);
        }, 1500);
      }

    } catch (error) {
      console.error('Failed to save vendor bill:', error);
      setSnackbar({
        open: true,
        message: `Failed to save vendor bill: ${error instanceof Error ? error.message : 'Unknown error'}`,
        severity: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  // Populate form data from selected PO
  const populateFormFromPO = (po: PurchaseOrder) => {
    // Find liability account (Accounts Payable)
    const liabilityAccount = liabilityAccounts.find(account =>
      account.account_name?.toLowerCase().includes('accounts payable') ||
      account.account_name?.toLowerCase().includes('payable')
    );

    // Find matching payment term
    let paymentTermId = null;
    let paymentTermName = po.payment_terms || 'Net 30';

    if (paymentTerms.length > 0) {
      const matchingTerm = paymentTerms.find(pt =>
        pt.name === po.payment_terms ||
        pt.code === po.payment_terms ||
        pt.description?.includes(po.payment_terms || '')
      );

      if (matchingTerm) {
        paymentTermId = matchingTerm.id;
        paymentTermName = matchingTerm.name;
      } else {
        // Use default payment term
        const defaultTerm = getDefaultPaymentTerm() || paymentTerms.find(pt => pt.code === 'net_30') || paymentTerms[0];
        if (defaultTerm) {
          paymentTermId = defaultTerm.id;
          paymentTermName = defaultTerm.name;
        }
      }
    }

    const billDate = dayjs().format('YYYY-MM-DD');
    const dueDate = calculateDueDate(billDate, paymentTermId);

    setFormData({
      vendor_id: po.vendor,
      vendor_name: po.vendor_name || '',
      liability_account_id: liabilityAccount?.id || null,
      bill_date: billDate,
      due_date: dueDate,
      reference_number: po.reference_number || `PO-${po.po_number}`,
      status: 'draft',
      payment_terms_id: paymentTermId,
      payment_terms_name: paymentTermName,
      notes: `Bill created from Purchase Order ${po.po_number}${po.memo ? `\n\nPO Notes: ${po.memo}` : ''}`,
      line_items: po.line_items?.map((item, index) => ({
        id: (index + 1).toString(),
        product_id: item.product || null,
        product_name: (item as any).product_name || '',
        item_description: item.description,
        quantity: item.quantity,
        unit_price: item.unit_price,
        line_total: item.line_total,
        tax_rate: item.tax_rate || 0,
        tax_amount: item.tax_amount || 0,
        account_code: (item as any).account_code || '5010-COGS',
      })) || [],
      subtotal: po.subtotal || 0,
      tax_amount: po.tax_amount || 0,
      total_amount: po.total_amount || 0,
      source_type: 'po',
      source_document_id: po.id || null,
      po_number: po.po_number,
    });
  };

  // Handle PO selection from the list
  const handlePOSelection = (po: PurchaseOrder) => {
    setSelectedPO(po);
    setShowPOSelection(false);

    // Auto-populate form data from selected PO
    populateFormFromPO(po);

    setSnackbar({
      open: true,
      message: `Purchase Order ${po.po_number} selected successfully!`,
      severity: 'success',
    });
  };

  if (poLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
        <Typography sx={{ ml: 2 }}>Loading Purchase Order...</Typography>
      </Box>
    );
  }

  if (!selectedPO) {
    if (showPOSelection) {
      // Show PO selection interface
      return (
        <Box sx={{ minHeight: '100vh', bgcolor: '#f5f5f5' }}>
          {/* Header */}
          <Box sx={{ bgcolor: 'white', borderBottom: 1, borderColor: 'divider', p: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Box>
                <Typography variant="h4" component="h1" sx={{ fontWeight: 600, color: '#1a1a1a' }}>
                  Select Purchase Order
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                  Choose a Purchase Order to create vendor bill from
                </Typography>
              </Box>
              <Button
                variant="outlined"
                startIcon={<ArrowBackIcon />}
                onClick={() => navigate('/dashboard/purchases/vendor-bills')}
                sx={{ borderRadius: '8px' }}
              >
                Back to Vendor Bills
              </Button>
            </Box>
          </Box>

          {/* PO Selection Content */}
          <Box sx={{ p: 3 }}>
            <Grid container spacing={3}>
              {availablePOs.length === 0 ? (
                <Grid item xs={12}>
                  <Paper sx={{ p: 4, textAlign: 'center' }}>
                    <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
                      No Purchase Orders Available
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                      There are no received or acknowledged Purchase Orders available for bill creation.
                    </Typography>
                    <Button
                      variant="contained"
                      onClick={() => navigate('/dashboard/purchases/purchase-orders')}
                      sx={{ borderRadius: '8px' }}
                    >
                      View Purchase Orders
                    </Button>
                  </Paper>
                </Grid>
              ) : (
                availablePOs.map((po) => (
                  <Grid item xs={12} md={6} lg={4} key={po.id}>
                    <Card
                      sx={{
                        cursor: 'pointer',
                        transition: 'all 0.2s',
                        '&:hover': {
                          transform: 'translateY(-2px)',
                          boxShadow: 4,
                        }
                      }}
                      onClick={() => handlePOSelection(po)}
                    >
                      <CardContent>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                          <Typography variant="h6" sx={{ fontWeight: 600 }}>
                            {po.po_number}
                          </Typography>
                          <Chip
                            label={po.status}
                            size="small"
                            color={po.status === 'received' ? 'success' : 'primary'}
                            sx={{ textTransform: 'capitalize' }}
                          />
                        </Box>

                        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                          <strong>Vendor:</strong> {po.vendor_name}
                        </Typography>

                        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                          <strong>Date:</strong> {dayjs(po.po_date).format('DD/MM/YYYY')}
                        </Typography>

                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                          <strong>Total:</strong> {formatCurrency(po.total_amount, currencyInfo?.currencySymbol || '$')}
                        </Typography>

                        <Button
                          variant="contained"
                          fullWidth
                          size="small"
                          sx={{ borderRadius: '6px' }}
                        >
                          Create Bill from this PO
                        </Button>
                      </CardContent>
                    </Card>
                  </Grid>
                ))
              )}
            </Grid>
          </Box>

          {/* Snackbar */}
          <Snackbar
            open={snackbar.open}
            autoHideDuration={6000}
            onClose={handleCloseSnackbar}
            anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
          >
            <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
              {snackbar.message}
            </Alert>
          </Snackbar>
        </Box>
      );
    } else {
      // Show error when specific PO not found
      return (
        <Box sx={{ p: 3 }}>
          <Alert severity="error">
            Purchase Order not found. Please check the PO ID and try again.
          </Alert>
          <Button
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate('/dashboard/purchases/purchase-orders')}
            sx={{ mt: 2 }}
          >
            Back to Purchase Orders
          </Button>
        </Box>
      );
    }
  }

  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column', bgcolor: '#f5f5f5' }}>
      {/* Loading Backdrop */}
      <Backdrop sx={{ color: '#fff', zIndex: (theme) => theme.zIndex.drawer + 1 }} open={loading}>
        <CircularProgress color="inherit" />
        <Typography sx={{ ml: 2 }}>Processing...</Typography>
      </Backdrop>

      {/* Header */}
      <Box sx={{ bgcolor: 'white', borderBottom: '1px solid #e0e0e0', p: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Button
              startIcon={<ArrowBackIcon />}
              onClick={() => navigate('/purchase/purchase-orders')}
              sx={{ mr: 2 }}
            >
              Back to Purchase Orders
            </Button>
            <ReceiptIcon sx={{ mr: 1, color: 'primary.main' }} />
            <Typography variant="h5" sx={{ fontWeight: 600 }}>
              Create Vendor Bill from PO
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant="contained"
              startIcon={<CheckCircleIcon />}
              onClick={handleCreateFromPO}
              disabled={loading}
              sx={{
                bgcolor: 'success.main',
                '&:hover': { bgcolor: 'success.dark' },
              }}
            >
              Create Bill from PO
            </Button>
            <Button
              variant="outlined"
              startIcon={<SaveIcon />}
              onClick={() => handleSaveCustomized(false)}
              disabled={loading}
            >
              Save Customized
            </Button>
            <Button
              variant="outlined"
              onClick={() => handleSaveCustomized(true)}
              disabled={loading}
            >
              Save and Close
            </Button>
          </Box>
        </Box>
      </Box>

      {/* Main Content */}
      <Box sx={{ flex: 1, overflow: 'auto', p: 3 }}>
        <Grid container spacing={3}>
          {/* PO Information Card */}
          <Grid item xs={12}>
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <DescriptionIcon sx={{ mr: 1, color: 'primary.main' }} />
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Purchase Order Information
                  </Typography>
                  <Chip
                    label={selectedPO.status.toUpperCase()}
                    color={selectedPO.status === 'received' ? 'success' : 'primary'}
                    size="small"
                    sx={{ ml: 2 }}
                  />
                </Box>

                <Grid container spacing={2}>
                  <Grid item xs={12} md={3}>
                    <Typography variant="body2" color="text.secondary">PO Number</Typography>
                    <Typography variant="body1" sx={{ fontWeight: 600 }}>
                      {selectedPO.po_number}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <Typography variant="body2" color="text.secondary">Vendor</Typography>
                    <Typography variant="body1" sx={{ fontWeight: 600 }}>
                      {selectedPO.vendor_name}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <Typography variant="body2" color="text.secondary">PO Date</Typography>
                    <Typography variant="body1">
                      {dayjs(selectedPO.po_date).format('DD/MM/YYYY')}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <Typography variant="body2" color="text.secondary">Total Amount</Typography>
                    <Typography variant="body1" sx={{ fontWeight: 600, color: 'primary.main' }}>
                      {formatCurrency(selectedPO.total_amount, currencySymbol)}
                    </Typography>
                  </Grid>
                  {selectedPO.reference_number && (
                    <Grid item xs={12} md={6}>
                      <Typography variant="body2" color="text.secondary">Reference</Typography>
                      <Typography variant="body1">{selectedPO.reference_number}</Typography>
                    </Grid>
                  )}
                  {selectedPO.memo && (
                    <Grid item xs={12}>
                      <Typography variant="body2" color="text.secondary">PO Notes</Typography>
                      <Typography variant="body1">{selectedPO.memo}</Typography>
                    </Grid>
                  )}
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Quick Create Option */}
          <Grid item xs={12}>
            <Alert
              severity="info"
              sx={{ mb: 3 }}
              action={
                <Button
                  color="inherit"
                  size="small"
                  onClick={handleCreateFromPO}
                  disabled={loading}
                >
                  Create Now
                </Button>
              }
            >
              <Typography variant="body2">
                <strong>Quick Option:</strong> Click "Create Bill from PO" to automatically create a vendor bill
                with all PO details, or customize the details below before saving.
              </Typography>
            </Alert>
          </Grid>

          {/* Vendor Bill Form */}
          <Grid item xs={12}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                Customize Vendor Bill Details
              </Typography>

              <Grid container spacing={3}>
                {/* Bill Date */}
                <Grid item xs={12} md={4}>
                  <StandardDatePicker
                    label="Bill Date *"
                    value={dayjs(formData.bill_date)}
                    onChange={(date) => handleBillDateChange(date?.format('YYYY-MM-DD') || '')}
                    required
                    businessContext="general"
                    showQuickActions
                    dateFormat="DD/MM/YYYY"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: '8px',
                      },
                    }}
                  />
                </Grid>

                {/* Due Date */}
                <Grid item xs={12} md={4}>
                  <StandardDatePicker
                    label="Due Date *"
                    value={dayjs(formData.due_date)}
                    onChange={(date) => handleFieldChange('due_date', date?.format('YYYY-MM-DD'))}
                    required
                    businessContext="general"
                    showQuickActions
                    dateFormat="DD/MM/YYYY"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: '8px',
                      },
                    }}
                  />
                </Grid>

                {/* Status */}
                <Grid item xs={12} md={4}>
                  <TextField
                    fullWidth
                    select
                    label="Status *"
                    value={formData.status}
                    onChange={(e) => handleFieldChange('status', e.target.value)}
                    required
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: '8px',
                      },
                    }}
                  >
                    <MenuItem value="draft">Draft</MenuItem>
                    <MenuItem value="posted">Posted</MenuItem>
                  </TextField>
                </Grid>

                {/* Reference Number */}
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Reference Number"
                    value={formData.reference_number}
                    onChange={(e) => handleFieldChange('reference_number', e.target.value)}
                    placeholder="Vendor's invoice number"
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: '8px',
                      },
                    }}
                  />
                </Grid>

                {/* Payment Terms */}
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    select
                    label="Payment Terms *"
                    value={formData.payment_terms_id || ''}
                    onChange={(e) => handlePaymentTermsChange(Number(e.target.value))}
                    disabled={paymentTermsLoading}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: '8px',
                      },
                    }}
                  >
                    {paymentTermsLoading ? (
                      <MenuItem disabled>
                        <em>Loading payment terms...</em>
                      </MenuItem>
                    ) : paymentTerms.length === 0 ? (
                      <MenuItem disabled>
                        <em>No payment terms found</em>
                      </MenuItem>
                    ) : (
                      paymentTerms.map((term) => (
                        <MenuItem key={term.id} value={term.id}>
                          <Box>
                            <Typography variant="body1" sx={{ fontWeight: 600, fontSize: '1rem' }}>
                              {term.name}
                            </Typography>
                            <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.875rem' }}>
                              {term.days} days {term.description && `• ${term.description}`}
                            </Typography>
                          </Box>
                        </MenuItem>
                      ))
                    )}
                  </TextField>
                </Grid>

                {/* Liability Account */}
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    select
                    label="Liability Account *"
                    value={formData.liability_account_id || ''}
                    onChange={(e) => handleFieldChange('liability_account_id', Number(e.target.value))}
                    required
                    disabled={accountsLoading}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: '8px',
                      },
                    }}
                  >
                    {accountsLoading ? (
                      <MenuItem disabled>
                        <em>Loading accounts...</em>
                      </MenuItem>
                    ) : accounts.length === 0 ? (
                      <MenuItem disabled>
                        <em>No accounts found</em>
                      </MenuItem>
                    ) : (
                      accounts
                        .filter(account =>
                          account.account_type_name?.toLowerCase().includes('liability') ||
                          account.account_name?.toLowerCase().includes('payable')
                        )
                        .map((account) => (
                          <MenuItem key={account.id} value={account.id}>
                            {account.account_number} - {account.account_name}
                          </MenuItem>
                        ))
                    )}
                  </TextField>
                </Grid>

                {/* Notes */}
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    multiline
                    rows={3}
                    label="Notes"
                    value={formData.notes}
                    onChange={(e) => handleFieldChange('notes', e.target.value)}
                    placeholder="Add any notes or comments..."
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: '8px',
                      },
                    }}
                  />
                </Grid>
              </Grid>

              <Divider sx={{ my: 3 }} />

              {/* Line Items */}
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                Line Items
              </Typography>

              <VendorBillLineTable
                lineItems={formData.line_items}
                onLineItemsChange={handleLineItemsChange}
                allProducts={products}
                productsLoading={false}
                readOnly={false}
              />

              {/* Totals */}
              <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
                <Box sx={{ minWidth: 300 }}>
                  <Grid container spacing={1}>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">
                        Subtotal:
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" sx={{ textAlign: 'right' }}>
                        {formatCurrency(formData.subtotal, currencySymbol)}
                      </Typography>
                    </Grid>

                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">
                        Tax Amount:
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" sx={{ textAlign: 'right' }}>
                        {formatCurrency(formData.tax_amount, currencySymbol)}
                      </Typography>
                    </Grid>

                    <Grid item xs={12}>
                      <Divider sx={{ my: 1 }} />
                    </Grid>

                    <Grid item xs={6}>
                      <Typography variant="h6" sx={{ fontWeight: 600 }}>
                        Total Amount:
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="h6" sx={{ fontWeight: 600, textAlign: 'right', color: 'primary.main' }}>
                        {formatCurrency(formData.total_amount, currencySymbol)}
                      </Typography>
                    </Grid>
                  </Grid>
                </Box>
              </Box>
            </Paper>
          </Grid>
        </Grid>
      </Box>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default CreateVendorBillFromPOPage;